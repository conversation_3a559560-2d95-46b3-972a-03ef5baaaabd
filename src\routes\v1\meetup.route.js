const express = require('express');
const auth = require('../../middlewares/auth');
const validate = require('../../middlewares/validate');
const meetupValidation = require('../../validations/meetup.validation');
const meetupController = require('../../controllers/meetup.controller');

const router = express.Router();

router
  .route('/')
  .post(auth(), validate(meetupValidation.createMeetup), meetupController.createMeetup)
  .get(auth(), validate(meetupValidation.getMeetups), meetupController.getMeetups);

router
  .route('/:meetupId')
  .get(auth(), validate(meetupValidation.getMeetup), meetupController.getMeetup)
  .patch(auth(), validate(meetupValidation.updateMeetup), meetupController.updateMeetup)
  .delete(auth(), validate(meetupValidation.deleteMeetup), meetupController.deleteMeetup);

router.route('/:meetupId/rsvp').post(auth(), validate(meetupValidation.rsvpMeetup), meetupController.rsvpMeetup);

router
  .route('/:meetupId/participants/:userId')
  .delete(auth(), validate(meetupValidation.removeParticipant), meetupController.removeParticipant);

module.exports = router;

/**
 * @swagger
 * tags:
 *   name: Meetups
 *   description: Meetup management and RSVP operations
 */

/**
 * @swagger
 * /meetups:
 *   post:
 *     summary: Create a meetup
 *     description: Create a new meetup. Can be associated with a group or individual.
 *     tags: [Meetups]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - location
 *               - dateTime
 *             properties:
 *               title:
 *                 type: string
 *                 description: Meetup title
 *               description:
 *                 type: string
 *                 description: Meetup description
 *               location:
 *                 type: object
 *                 required:
 *                   - name
 *                   - address
 *                   - coordinates
 *                 properties:
 *                   name:
 *                     type: string
 *                   address:
 *                     type: string
 *                   coordinates:
 *                     type: object
 *                     properties:
 *                       lat:
 *                         type: number
 *                       lng:
 *                         type: number
 *                   placeId:
 *                     type: string
 *               dateTime:
 *                 type: string
 *                 format: date-time
 *               duration:
 *                 type: integer
 *                 description: Duration in minutes
 *                 default: 120
 *               groupId:
 *                 type: string
 *                 description: Associated group ID (optional)
 *               isPrivate:
 *                 type: boolean
 *                 default: false
 *               maxParticipants:
 *                 type: integer
 *                 description: Maximum number of participants
 *             example:
 *               title: "Tech Meetup"
 *               description: "Discussion about latest tech trends"
 *               location:
 *                 name: "Coffee Shop"
 *                 address: "123 Main St, City"
 *                 coordinates:
 *                   lat: 40.7128
 *                   lng: -74.0060
 *                 placeId: "ChIJN1t_tDeuEmsRUsoyG83frY4"
 *               dateTime: "2024-01-15T18:00:00.000Z"
 *               duration: 120
 *               maxParticipants: 20
 *     responses:
 *       "201":
 *         description: Created
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Meetup'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "400":
 *         $ref: '#/components/responses/ValidationError'
 *
 *   get:
 *     summary: Get all meetups
 *     description: Retrieve meetups with optional filtering and pagination
 *     tags: [Meetups]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: title
 *         schema:
 *           type: string
 *         description: Filter by meetup title
 *       - in: query
 *         name: createdBy
 *         schema:
 *           type: string
 *         description: Filter by creator user ID
 *       - in: query
 *         name: groupId
 *         schema:
 *           type: string
 *         description: Filter by group ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [Scheduled, Active, Completed, Cancelled]
 *         description: Filter by meetup status
 *       - in: query
 *         name: isPrivate
 *         schema:
 *           type: boolean
 *         description: Filter by privacy setting
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Sort by field (e.g., dateTime:asc, title:desc)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *         default: 10
 *         description: Maximum number of meetups
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         default: 1
 *         description: Page number
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 results:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Meetup'
 *                 page:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 totalPages:
 *                   type: integer
 *                 totalResults:
 *                   type: integer
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 */

/**
 * @swagger
 * /meetups/{id}:
 *   get:
 *     summary: Get a meetup
 *     description: Retrieve meetup details by ID
 *     tags: [Meetups]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Meetup ID
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Meetup'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *
 *   patch:
 *     summary: Update a meetup
 *     description: Only meetup creator or group admins can update meetup details
 *     tags: [Meetups]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Meetup ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               location:
 *                 type: object
 *                 properties:
 *                   name:
 *                     type: string
 *                   address:
 *                     type: string
 *                   coordinates:
 *                     type: object
 *                     properties:
 *                       lat:
 *                         type: number
 *                       lng:
 *                         type: number
 *                   placeId:
 *                     type: string
 *               dateTime:
 *                 type: string
 *                 format: date-time
 *               duration:
 *                 type: integer
 *               status:
 *                 type: string
 *                 enum: [Scheduled, Active, Completed, Cancelled]
 *               isPrivate:
 *                 type: boolean
 *               maxParticipants:
 *                 type: integer
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Meetup'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *
 *   delete:
 *     summary: Delete a meetup
 *     description: Only the meetup creator or group admin can delete the meetup
 *     tags: [Meetups]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Meetup ID
 *     responses:
 *       "204":
 *         description: No content
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * /meetups/{id}/rsvp:
 *   post:
 *     summary: RSVP to meetup
 *     description: Respond to meetup invitation with Accept, Decline, or Maybe
 *     tags: [Meetups]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Meetup ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - rsvpStatus
 *             properties:
 *               rsvpStatus:
 *                 type: string
 *                 enum: [Accept, Decline, Maybe]
 *                 description: RSVP response
 *             example:
 *               rsvpStatus: "Accept"
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Meetup'
 *       "400":
 *         description: Bad request - Meetup is full
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * /meetups/{id}/participants/{userId}:
 *   delete:
 *     summary: Remove participant from meetup
 *     description: Remove a participant from the meetup. Users can remove themselves, or creators/admins can remove others.
 *     tags: [Meetups]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Meetup ID
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID to remove from meetup
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Meetup'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */
