const Joi = require('joi');
const { objectId } = require('./custom.validation');

const createMeetup = {
  body: Joi.object().keys({
    title: Joi.string().required().trim(),
    description: Joi.string().trim(),
    location: Joi.object()
      .keys({
        name: Joi.string().required(),
        address: Joi.string().required(),
        coordinates: Joi.object()
          .keys({
            lat: Joi.number().required(),
            lng: Joi.number().required(),
          })
          .required(),
        placeId: Joi.string().allow(''),
      })
      .required(),
    dateTime: Joi.date().required(),
    duration: Joi.number().integer().min(1),
    groupId: Joi.string().custom(objectId).allow(''),
    isPrivate: Joi.boolean(),
    maxParticipants: Joi.number().integer().min(1),
  }),
};

const getMeetups = {
  query: Joi.object().keys({
    title: Joi.string(),
    createdBy: Joi.string().custom(objectId),
    groupId: Joi.string().custom(objectId),
    status: Joi.string().valid('Scheduled', 'Active', 'Completed', 'Cancelled'),
    isPrivate: Joi.boolean(),
    sortBy: Joi.string(),
    limit: Joi.number().integer(),
    page: Joi.number().integer(),
  }),
};

const getMeetup = {
  params: Joi.object().keys({
    meetupId: Joi.string().custom(objectId),
  }),
};

const updateMeetup = {
  params: Joi.object().keys({
    meetupId: Joi.required().custom(objectId),
  }),
  body: Joi.object()
    .keys({
      title: Joi.string().trim(),
      description: Joi.string().trim(),
      location: Joi.object().keys({
        name: Joi.string(),
        address: Joi.string(),
        coordinates: Joi.object().keys({
          lat: Joi.number(),
          lng: Joi.number(),
        }),
        placeId: Joi.string(),
      }),
      dateTime: Joi.date(),
      duration: Joi.number().integer().min(1),
      status: Joi.string().valid('Scheduled', 'Active', 'Completed', 'Cancelled'),
      isPrivate: Joi.boolean(),
      maxParticipants: Joi.number().integer().min(1),
    })
    .min(1),
};

const deleteMeetup = {
  params: Joi.object().keys({
    meetupId: Joi.string().custom(objectId),
  }),
};

const rsvpMeetup = {
  params: Joi.object().keys({
    meetupId: Joi.string().custom(objectId),
  }),
  body: Joi.object().keys({
    rsvpStatus: Joi.string().valid('Accept', 'Decline', 'Maybe').required(),
  }),
};

const removeParticipant = {
  params: Joi.object().keys({
    meetupId: Joi.string().custom(objectId),
    userId: Joi.string().custom(objectId),
  }),
};

module.exports = {
  createMeetup,
  getMeetups,
  getMeetup,
  updateMeetup,
  deleteMeetup,
  rsvpMeetup,
  removeParticipant,
};


