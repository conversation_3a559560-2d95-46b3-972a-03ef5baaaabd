const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
// Meetup Model
const meetupSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    location: {
      name: {
        type: String,
        required: true,
      },
      address: {
        type: String,
        required: true,
      },
      coordinates: {
        lat: {
          type: Number,
          required: true,
        },
        lng: {
          type: Number,
          required: true,
        },
      },
      placeId: {
        type: String, // Google Places API place ID
      },
    },
    dateTime: {
      type: Date,
      required: true,
    },
    duration: {
      type: Number, // Duration in minutes
      default: 120,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    groupId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Group',
      default: null, // null for individual meetups
    },
    participants: [
      {
        userId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'User',
          required: true,
        },
        rsvpStatus: {
          type: String,
          enum: ['Accept', 'Decline', 'Maybe', 'Pending'],
          default: 'Pending',
        },
        rsvpAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    status: {
      type: String,
      enum: ['Scheduled', 'Active', 'Completed', 'Cancelled'],
      default: 'Scheduled',
    },
    isPrivate: {
      type: Boolean,
      default: false,
    },
    maxParticipants: {
      type: Number,
      default: null, // null for unlimited
    },
  },
  {
    timestamps: true,
  }
);

// add plugin that converts mongoose to json
meetupSchema.plugin(toJSON);
meetupSchema.plugin(paginate);

/**
 * @typedef Meetup
 */
const Meetup = mongoose.model('Meetup', meetupSchema);

module.exports = Meetup;
