const httpStatus = require('http-status');
const { Meetup, Group } = require('../models');
const ApiError = require('../utils/ApiError');

/**
 * Create a meetup
 * @param {Object} meetupBody
 * @param {string} userId
 * @returns {Promise<Meetup>}
 */
const createMeetup = async (meetupBody, userId) => {
  // Convert empty groupId to null for Mongoose
  if (meetupBody.groupId === '') {
    // eslint-disable-next-line no-param-reassign
    meetupBody.groupId = null;
  }

  // If groupId is provided, check if user is a member
  if (meetupBody.groupId) {
    const group = await Group.findById(meetupBody.groupId);
    if (!group) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Group not found');
    }

    const isMember = group.members.some((member) => member.userId.toString() === userId);
    if (!isMember) {
      throw new ApiError(httpStatus.FORBIDDEN, 'You must be a group member to create meetups for this group');
    }
  }

  const meetup = await Meetup.create({
    ...meetupBody,
    createdBy: userId,
    participants: [{ userId, rsvpStatus: 'Accept' }], // Creator automatically accepts
  });

  return meetup;
};

/**
 * Query for meetups
 * @param {Object} filter - Mongo filter
 * @param {Object} options - Query options
 * @returns {Promise<QueryResult>}
 */
const queryMeetups = async (filter, options) => {
  const meetups = await Meetup.paginate(filter, options);
  return meetups;
};

/**
 * Get meetup by id
 * @param {ObjectId} id
 * @returns {Promise<Meetup>}
 */
const getMeetupById = async (id) => {
  return Meetup.findById(id)
    .populate('createdBy', 'name email')
    .populate('groupId', 'name description')
    .populate('participants.userId', 'name email');
};

/**
 * Update meetup by id
 * @param {ObjectId} meetupId
 * @param {Object} updateBody
 * @param {string} userId
 * @returns {Promise<Meetup>}
 */
const updateMeetupById = async (meetupId, updateBody, userId) => {
  const meetup = await getMeetupById(meetupId);
  if (!meetup) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Meetup not found');
  }

  // Check if user is the creator or group admin
  let canUpdate = meetup.createdBy._id.toString() === userId;

  if (!canUpdate && meetup.groupId) {
    const group = await Group.findById(meetup.groupId);
    const userMember = group.members.find((member) => member.userId.toString() === userId);
    canUpdate = userMember && (userMember.role === 'Admin' || userMember.role === 'Moderator');
  }

  if (!canUpdate) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You do not have permission to update this meetup');
  }

  Object.assign(meetup, updateBody);
  await meetup.save();
  return meetup;
};

/**
 * Delete meetup by id
 * @param {ObjectId} meetupId
 * @param {string} userId
 * @returns {Promise<Meetup>}
 */
const deleteMeetupById = async (meetupId, userId) => {
  const meetup = await getMeetupById(meetupId);
  if (!meetup) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Meetup not found');
  }

  // Check if user is the creator or group admin
  let canDelete = meetup.createdBy._id.toString() === userId;

  if (!canDelete && meetup.groupId) {
    const group = await Group.findById(meetup.groupId);
    const userMember = group.members.find((member) => member.userId.toString() === userId);
    canDelete = userMember && userMember.role === 'Admin';
  }

  if (!canDelete) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You do not have permission to delete this meetup');
  }

  await meetup.remove();
  return meetup;
};

/**
 * RSVP to meetup
 * @param {ObjectId} meetupId
 * @param {string} rsvpStatus
 * @param {string} userId
 * @returns {Promise<Meetup>}
 */
const rsvpToMeetup = async (meetupId, rsvpStatus, userId) => {
  const meetup = await getMeetupById(meetupId);
  if (!meetup) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Meetup not found');
  }

  // Check if meetup is full
  if (meetup.maxParticipants && rsvpStatus === 'Accept') {
    const acceptedCount = meetup.participants.filter((p) => p.rsvpStatus === 'Accept').length;
    if (acceptedCount >= meetup.maxParticipants) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Meetup is full');
    }
  }

  // Check if user is already a participant
  const existingParticipant = meetup.participants.find((p) => p.userId._id.toString() === userId);

  if (existingParticipant) {
    existingParticipant.rsvpStatus = rsvpStatus;
    existingParticipant.rsvpAt = new Date();
  } else {
    meetup.participants.push({
      userId,
      rsvpStatus,
      rsvpAt: new Date(),
    });
  }

  await meetup.save();
  return meetup;
};

/**
 * Remove participant from meetup
 * @param {ObjectId} meetupId
 * @param {ObjectId} participantUserId
 * @param {string} userId
 * @returns {Promise<Meetup>}
 */
const removeParticipantFromMeetup = async (meetupId, participantUserId, userId) => {
  const meetup = await getMeetupById(meetupId);
  if (!meetup) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Meetup not found');
  }

  const participant = meetup.participants.find((p) => p.userId._id.toString() === participantUserId);
  if (!participant) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Participant not found in meetup');
  }

  // Check permissions - creator, group admin, or the participant themselves
  let canRemove = meetup.createdBy._id.toString() === userId || participantUserId === userId;

  if (!canRemove && meetup.groupId) {
    const group = await Group.findById(meetup.groupId);
    const userMember = group.members.find((member) => member.userId.toString() === userId);
    canRemove = userMember && (userMember.role === 'Admin' || userMember.role === 'Moderator');
  }

  if (!canRemove) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You do not have permission to remove this participant');
  }

  meetup.participants = meetup.participants.filter((p) => p.userId._id.toString() !== participantUserId);
  await meetup.save();
  return meetup;
};

module.exports = {
  createMeetup,
  queryMeetups,
  getMeetupById,
  updateMeetupById,
  deleteMeetupById,
  rsvpToMeetup,
  removeParticipantFromMeetup,
};

