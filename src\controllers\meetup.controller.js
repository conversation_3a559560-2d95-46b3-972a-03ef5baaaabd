const httpStatus = require('http-status');
const pick = require('../utils/pick');
const ApiError = require('../utils/ApiError');
const catchAsync = require('../utils/catchAsync');
const { meetupService } = require('../services');

const createMeetup = catchAsync(async (req, res) => {
  const meetup = await meetupService.createMeetup(req.body, req.user.id);
  res.status(httpStatus.CREATED).send(meetup);
});

const getMeetups = catchAsync(async (req, res) => {
  const filter = pick(req.query, ['title', 'createdBy', 'groupId', 'status', 'isPrivate']);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const result = await meetupService.queryMeetups(filter, options);
  res.send(result);
});

const getMeetup = catchAsync(async (req, res) => {
  const meetup = await meetupService.getMeetupById(req.params.meetupId);
  if (!meetup) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Meetup not found');
  }
  res.send(meetup);
});

const updateMeetup = catchAsync(async (req, res) => {
  const meetup = await meetupService.updateMeetupById(req.params.meetupId, req.body, req.user.id);
  res.send(meetup);
});

const deleteMeetup = catchAsync(async (req, res) => {
  await meetupService.deleteMeetupById(req.params.meetupId, req.user.id);
  res.status(httpStatus.NO_CONTENT).send();
});

const rsvpMeetup = catchAsync(async (req, res) => {
  const meetup = await meetupService.rsvpToMeetup(req.params.meetupId, req.body.rsvpStatus, req.user.id);
  res.send(meetup);
});

const removeParticipant = catchAsync(async (req, res) => {
  const meetup = await meetupService.removeParticipantFromMeetup(req.params.meetupId, req.params.userId, req.user.id);
  res.send(meetup);
});

module.exports = {
  createMeetup,
  getMeetups,
  getMeetup,
  updateMeetup,
  deleteMeetup,
  rsvpMeetup,
  removeParticipant,
};
