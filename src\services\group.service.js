const httpStatus = require('http-status');
const { Group } = require('../models');
const ApiError = require('../utils/ApiError');

/**
 * Create a group
 * @param {Object} groupBody
 * @param {string} userId
 * @returns {Promise<Group>}
 */
const createGroup = async (groupBody, userId) => {
  const group = await Group.create({
    ...groupBody,
    createdBy: userId,
    members: [{ userId, role: 'Admin' }],
  });
  return group;
};

/**
 * Query for groups
 * @param {Object} filter - Mongo filter
 * @param {Object} options - Query options
 * @returns {Promise<QueryResult>}
 */
const queryGroups = async (filter, options) => {
  const groups = await Group.paginate(filter, options);
  return groups;
};

/**
 * Get group by id
 * @param {ObjectId} id
 * @returns {Promise<Group>}
 */
const getGroupById = async (id) => {
  return Group.findById(id).populate('createdBy', 'name email').populate('members.userId', 'name email');
};

/**
 * Update group by id
 * @param {ObjectId} groupId
 * @param {Object} updateBody
 * @param {string} userId
 * @returns {Promise<Group>}
 */
const updateGroupById = async (groupId, updateBody, userId) => {
  const group = await getGroupById(groupId);
  if (!group) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Group not found');
  }

  // Check if user is admin of the group
  const isAdmin = group.members.some((member) => member.userId._id.toString() === userId && member.role === 'Admin');
  if (!isAdmin) {
    throw new ApiError(httpStatus.FORBIDDEN, 'Only group admins can update the group');
  }

  Object.assign(group, updateBody);
  await group.save();
  return group;
};

/**
 * Delete group by id
 * @param {ObjectId} groupId
 * @param {string} userId
 * @returns {Promise<Group>}
 */
const deleteGroupById = async (groupId, userId) => {
  const group = await getGroupById(groupId);
  if (!group) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Group not found');
  }

  // Only creator can delete the group
  if (group.createdBy._id.toString() !== userId) {
    throw new ApiError(httpStatus.FORBIDDEN, 'Only group creator can delete the group');
  }

  await group.remove();
  return group;
};

/**
 * Add member to group
 * @param {ObjectId} groupId
 * @param {Object} memberData
 * @param {string} userId
 * @returns {Promise<Group>}
 */
const addMemberToGroup = async (groupId, memberData, userId) => {
  const group = await getGroupById(groupId);
  if (!group) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Group not found');
  }

  // Check if user has permission to add members
  const userMember = group.members.find((member) => member.userId._id.toString() === userId);
  if (!userMember || (userMember.role === 'Member' && !group.settings.allowMemberInvites)) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You do not have permission to add members');
  }

  // Check if user is already a member
  const existingMember = group.members.find((member) => member.userId._id.toString() === memberData.userId);
  if (existingMember) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'User is already a member of this group');
  }

  group.members.push(memberData);
  await group.save();
  return group;
};

/**
 * Remove member from group
 * @param {ObjectId} groupId
 * @param {ObjectId} memberUserId
 * @param {string} userId
 * @returns {Promise<Group>}
 */
const removeMemberFromGroup = async (groupId, memberUserId, userId) => {
  const group = await getGroupById(groupId);
  if (!group) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Group not found');
  }

  const userMember = group.members.find((member) => member.userId._id.toString() === userId);
  const targetMember = group.members.find((member) => member.userId._id.toString() === memberUserId);

  if (!targetMember) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Member not found in group');
  }

  // Check permissions
  if (userId !== memberUserId && (!userMember || userMember.role === 'Member')) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You do not have permission to remove this member');
  }

  group.members = group.members.filter((member) => member.userId._id.toString() !== memberUserId);
  await group.save();
  return group;
};

module.exports = {
  createGroup,
  queryGroups,
  getGroupById,
  updateGroupById,
  deleteGroupById,
  addMemberToGroup,
  removeMemberFromGroup,
};
