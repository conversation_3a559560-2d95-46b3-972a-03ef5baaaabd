components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: string
        email:
          type: string
          format: email
        name:
          type: string
        role:
          type: string
          enum: [user, admin]
      example:
        id: 5ebac534954b54139806c112
        email: <EMAIL>
        name: fake name
        role: user

    Group:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        avatar:
          type: string
          format: uri
        createdBy:
          $ref: '#/components/schemas/User'
        members:
          type: array
          items:
            type: object
            properties:
              userId:
                $ref: '#/components/schemas/User'
              role:
                type: string
                enum: [Admin, Moderator, Member]
              joinedAt:
                type: string
                format: date-time
        settings:
          type: object
          properties:
            isPrivate:
              type: boolean
            allowMemberInvites:
              type: boolean
            requireApproval:
              type: boolean
        isActive:
          type: boolean
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
      example:
        id: 5ebac534954b54139806c113
        name: "Tech Enthusiasts"
        description: "A group for technology discussions"
        avatar: "https://example.com/avatar.jpg"
        createdBy:
          id: 5ebac534954b54139806c112
          name: "<PERSON>"
          email: "<EMAIL>"
        members:
          - userId:
              id: 5ebac534954b54139806c112
              name: "John Doe"
              email: "<EMAIL>"
            role: "Admin"
            joinedAt: "2020-05-12T16:18:04.793Z"
        settings:
          isPrivate: false
          allowMemberInvites: true
          requireApproval: false
        isActive: true
        createdAt: "2020-05-12T16:18:04.793Z"
        updatedAt: "2020-05-12T16:18:04.793Z"

    Token:
      type: object
      properties:
        token:
          type: string
        expires:
          type: string
          format: date-time
      example:
        token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI1ZWJhYzUzNDk1NGI1NDEzOTgwNmMxMTIiLCJpYXQiOjE1ODkyOTg0ODQsImV4cCI6MTU4OTMwMDI4NH0.m1U63blB0MLej_WfB7yC2FTMnCziif9X8yzwDEfJXAg
        expires: 2020-05-12T16:18:04.793Z

    AuthTokens:
      type: object
      properties:
        access:
          $ref: '#/components/schemas/Token'
        refresh:
          $ref: '#/components/schemas/Token'

    Error:
      type: object
      properties:
        code:
          type: number
        message:
          type: string

    Meetup:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        description:
          type: string
        location:
          type: object
          properties:
            name:
              type: string
            address:
              type: string
            coordinates:
              type: object
              properties:
                lat:
                  type: number
                lng:
                  type: number
            placeId:
              type: string
        dateTime:
          type: string
          format: date-time
        duration:
          type: integer
          description: Duration in minutes
        createdBy:
          $ref: '#/components/schemas/User'
        groupId:
          $ref: '#/components/schemas/Group'
        participants:
          type: array
          items:
            type: object
            properties:
              userId:
                $ref: '#/components/schemas/User'
              rsvpStatus:
                type: string
                enum: [Accept, Decline, Maybe, Pending]
              rsvpAt:
                type: string
                format: date-time
        status:
          type: string
          enum: [Scheduled, Active, Completed, Cancelled]
        isPrivate:
          type: boolean
        maxParticipants:
          type: integer
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
      example:
        id: 5ebac534954b54139806c114
        title: "Tech Meetup"
        description: "Discussion about latest tech trends"
        location:
          name: "Coffee Shop"
          address: "123 Main St, City"
          coordinates:
            lat: 40.7128
            lng: -74.0060
          placeId: "ChIJN1t_tDeuEmsRUsoyG83frY4"
        dateTime: "2024-01-15T18:00:00.000Z"
        duration: 120
        createdBy:
          id: 5ebac534954b54139806c112
          name: "John Doe"
          email: "<EMAIL>"
        participants:
          - userId:
              id: 5ebac534954b54139806c112
              name: "John Doe"
              email: "<EMAIL>"
            rsvpStatus: "Accept"
            rsvpAt: "2024-01-10T10:00:00.000Z"
        status: "Scheduled"
        isPrivate: false
        maxParticipants: 20
        createdAt: "2024-01-10T10:00:00.000Z"
        updatedAt: "2024-01-10T10:00:00.000Z"

  responses:
    DuplicateEmail:
      description: Email already taken
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 400
            message: Email already taken
    
    ValidationError:
      description: Validation Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 400
            message: Validation failed
    
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 401
            message: Please authenticate
    
    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 403
            message: Forbidden
    
    NotFound:
      description: Not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 404
            message: Not found

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT


