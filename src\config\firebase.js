const admin = require('firebase-admin');
const config = require('./config');

const serviceAccount = {
  type: 'service_account',
  project_id: config.firebase.projectId,
  private_key_id: config.firebase.privateKeyId,
  private_key: config.firebase.privateKey,
  client_email: config.firebase.clientEmail,
  client_id: config.firebase.clientId,
  auth_uri: config.firebase.authUri,
  token_uri: config.firebase.tokenUri,
  auth_provider_x509_cert_url: `https://www.googleapis.com/oauth2/v1/certs`,
  client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${config.firebase.clientEmail}`,
};

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  projectId: config.firebase.projectId,
});

module.exports = admin;
