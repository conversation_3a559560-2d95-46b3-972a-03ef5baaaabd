const express = require('express');
const auth = require('../../middlewares/auth');
const validate = require('../../middlewares/validate');
const groupValidation = require('../../validations/group.validation');
const groupController = require('../../controllers/group.controller');

const router = express.Router();

router
  .route('/')
  .post(auth(), validate(groupValidation.createGroup), groupController.createGroup)
  .get(auth(), validate(groupValidation.getGroups), groupController.getGroups);

router
  .route('/:groupId')
  .get(auth(), validate(groupValidation.getGroup), groupController.getGroup)
  .patch(auth(), validate(groupValidation.updateGroup), groupController.updateGroup)
  .delete(auth(), validate(groupValidation.deleteGroup), groupController.deleteGroup);

router.route('/:groupId/members').post(auth(), validate(groupValidation.addMember), groupController.addMember);

router
  .route('/:groupId/members/:userId')
  .delete(auth(), validate(groupValidation.removeMember), groupController.removeMember);

module.exports = router;

/**
 * @swagger
 * tags:
 *   name: Groups
 *   description: Group management and member operations
 */

/**
 * @swagger
 * /groups:
 *   post:
 *     summary: Create a group
 *     description: Create a new group. The creator automatically becomes an admin.
 *     tags: [Groups]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 description: Group name
 *               description:
 *                 type: string
 *                 description: Group description
 *               avatar:
 *                 type: string
 *                 format: uri
 *                 description: Group avatar URL
 *               settings:
 *                 type: object
 *                 properties:
 *                   isPrivate:
 *                     type: boolean
 *                     default: false
 *                   allowMemberInvites:
 *                     type: boolean
 *                     default: true
 *                   requireApproval:
 *                     type: boolean
 *                     default: false
 *             example:
 *               name: "Tech Enthusiasts"
 *               description: "A group for technology discussions"
 *               avatar: "https://example.com/avatar.jpg"
 *               settings:
 *                 isPrivate: false
 *                 allowMemberInvites: true
 *                 requireApproval: false
 *     responses:
 *       "201":
 *         description: Created
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Group'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "400":
 *         $ref: '#/components/responses/ValidationError'
 *
 *   get:
 *     summary: Get all groups
 *     description: Retrieve groups with optional filtering and pagination
 *     tags: [Groups]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: name
 *         schema:
 *           type: string
 *         description: Filter by group name
 *       - in: query
 *         name: createdBy
 *         schema:
 *           type: string
 *         description: Filter by creator user ID
 *       - in: query
 *         name: isPrivate
 *         schema:
 *           type: boolean
 *         description: Filter by privacy setting
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Sort by field (e.g., name:asc, createdAt:desc)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *         default: 10
 *         description: Maximum number of groups
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         default: 1
 *         description: Page number
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 results:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Group'
 *                 page:
 *                   type: integer
 *                   example: 1
 *                 limit:
 *                   type: integer
 *                   example: 10
 *                 totalPages:
 *                   type: integer
 *                   example: 1
 *                 totalResults:
 *                   type: integer
 *                   example: 1
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 */

/**
 * @swagger
 * /groups/{id}:
 *   get:
 *     summary: Get a group
 *     description: Retrieve group details by ID
 *     tags: [Groups]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Group ID
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Group'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *
 *   patch:
 *     summary: Update a group
 *     description: Only group admins can update group details
 *     tags: [Groups]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Group ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               avatar:
 *                 type: string
 *                 format: uri
 *               settings:
 *                 type: object
 *                 properties:
 *                   isPrivate:
 *                     type: boolean
 *                   allowMemberInvites:
 *                     type: boolean
 *                   requireApproval:
 *                     type: boolean
 *             example:
 *               name: "Updated Group Name"
 *               description: "Updated description"
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Group'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *
 *   delete:
 *     summary: Delete a group
 *     description: Only the group creator can delete the group
 *     tags: [Groups]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Group ID
 *     responses:
 *       "204":
 *         description: No content
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * /groups/{id}/members:
 *   post:
 *     summary: Add member to group
 *     description: Add a new member to the group. Requires appropriate permissions.
 *     tags: [Groups]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Group ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *             properties:
 *               userId:
 *                 type: string
 *                 description: User ID to add as member
 *               role:
 *                 type: string
 *                 enum: [Admin, Moderator, Member]
 *                 default: Member
 *                 description: Role to assign to the new member
 *             example:
 *               userId: "5ebac534954b54139806c112"
 *               role: "Member"
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Group'
 *       "400":
 *         description: Bad request - User already a member
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * /groups/{id}/members/{userId}:
 *   delete:
 *     summary: Remove member from group
 *     description: Remove a member from the group. Users can remove themselves, or admins/moderators can remove others.
 *     tags: [Groups]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Group ID
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID to remove from group
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Group'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */
