const { Strategy: JwtStrategy, ExtractJwt } = require('passport-jwt');
const config = require('./config');
const { tokenTypes } = require('./tokens');
const { User } = require('../models');

const jwtOptions = {
  secretOrKey: config.jwt.secret,
  jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
};

const jwtVerify = async (payload, done) => {
  try {
    // eslint-disable-next-line no-console
    console.log('JWT Verify - Payload:', payload);

    if (payload.type !== tokenTypes.ACCESS) {
      // eslint-disable-next-line no-console
      console.log('JWT Verify - Invalid token type:', payload.type);
      throw new Error('Invalid token type');
    }

    const user = await User.findById(payload.sub);
    // eslint-disable-next-line no-console
    console.log('JWT Verify - User found:', !!user);

    if (!user) {
      return done(null, false);
    }
    done(null, user);
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log('JWT Verify - Error:', error.message);
    done(error, false);
  }
};

const jwtStrategy = new JwtStrategy(jwtOptions, jwtVerify);

module.exports = {
  jwtStrategy,
};
