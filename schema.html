<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meetup App Schema Flowchart</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/react/18.2.0/umd/react.production.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/react-dom/18.2.0/umd/react-dom.production.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/excalidraw/0.17.0/excalidraw.production.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        
        .header p {
            margin: 8px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        
        .container {
            height: calc(100vh - 120px);
            background: white;
            margin: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .instructions {
            background: #e3f2fd;
            padding: 15px 20px;
            border-left: 4px solid #1976d2;
            margin: 20px;
            border-radius: 8px;
            font-size: 14px;
            color: #1565c0;
        }
        
        .legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            padding: 15px;
            background: #f5f5f5;
            border-bottom: 1px solid #e0e0e0;
            flex-wrap: wrap;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .user { background: #e1f5fe; color: #0277bd; }
        .group { background: #f3e5f5; color: #7b1fa2; }
        .meetup { background: #e8f5e8; color: #388e3c; }
        .location { background: #fff3e0; color: #f57c00; }
        .message { background: #fce4ec; color: #c2185b; }
        .notification { background: #f1f8e9; color: #689f38; }
        
        .color-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎉 Meetup App - MongoDB Schema Architecture</h1>
        <p>Interactive flowchart showing database relationships and data flow</p>
    </div>
    
    <div class="instructions">
        <strong>📋 Instructions:</strong> This Excalidraw diagram shows your MongoDB schema relationships. You can zoom, pan, and edit elements. Each color represents different data domains in your meetup application.
    </div>
    
    <div class="legend">
        <div class="legend-item user"><div class="color-dot user"></div>User Data</div>
        <div class="legend-item group"><div class="color-dot group"></div>Group Management</div>
        <div class="legend-item meetup"><div class="color-dot meetup"></div>Meetup Core</div>
        <div class="legend-item location"><div class="color-dot location"></div>Location Tracking</div>
        <div class="legend-item message"><div class="color-dot message"></div>Communication</div>
        <div class="legend-item notification"><div class="color-dot notification"></div>Notifications</div>
    </div>
    
    <div class="container" id="excalidraw-container"></div>

    <script>
        // Initial Excalidraw data with the schema flowchart
        const initialData = {
            "type": "excalidraw",
            "version": 2,
            "source": "https://excalidraw.com",
            "elements": [
                // User Schema - Central top
                {
                    "id": "user-schema",
                    "type": "rectangle",
                    "x": 400,
                    "y": 50,
                    "width": 200,
                    "height": 80,
                    "angle": 0,
                    "strokeColor": "#0277bd",
                    "backgroundColor": "#e1f5fe",
                    "fillStyle": "solid",
                    "strokeWidth": 2,
                    "strokeStyle": "solid",
                    "roughness": 1,
                    "opacity": 100
                },
                {
                    "id": "user-text",
                    "type": "text",
                    "x": 460,
                    "y": 80,
                    "width": 80,
                    "height": 25,
                    "angle": 0,
                    "strokeColor": "#0277bd",
                    "backgroundColor": "transparent",
                    "fillStyle": "solid",
                    "strokeWidth": 2,
                    "strokeStyle": "solid",
                    "roughness": 1,
                    "opacity": 100,
                    "text": "👤 User\nSchema",
                    "fontSize": 16,
                    "fontFamily": 1,
                    "textAlign": "center",
                    "verticalAlign": "middle"
                },
                
                // Group Schema - Left side
                {
                    "id": "group-schema",
                    "type": "rectangle",
                    "x": 100,
                    "y": 200,
                    "width": 180,
                    "height": 80,
                    "angle": 0,
                    "strokeColor": "#7b1fa2",
                    "backgroundColor": "#f3e5f5",
                    "fillStyle": "solid",
                    "strokeWidth": 2,
                    "strokeStyle": "solid",
                    "roughness": 1,
                    "opacity": 100
                },
                {
                    "id": "group-text",
                    "type": "text",
                    "x": 150,
                    "y": 230,
                    "width": 80,
                    "height": 25,
                    "angle": 0,
                    "strokeColor": "#7b1fa2",
                    "backgroundColor": "transparent",
                    "fillStyle": "solid",
                    "strokeWidth": 2,
                    "strokeStyle": "solid",
                    "roughness": 1,
                    "opacity": 100,
                    "text": "👥 Group\nSchema",
                    "fontSize": 16,
                    "fontFamily": 1,
                    "textAlign": "center",
                    "verticalAlign": "middle"
                },
                
                // Meetup Schema - Center
                {
                    "id": "meetup-schema",
                    "type": "rectangle",
                    "x": 400,
                    "y": 200,
                    "width": 200,
                    "height": 80,
                    "angle": 0,
                    "strokeColor": "#388e3c",
                    "backgroundColor": "#e8f5e8",
                    "fillStyle": "solid",
                    "strokeWidth": 2,
                    "strokeStyle": "solid",
                    "roughness": 1,
                    "opacity": 100
                },
                {
                    "id": "meetup-text",
                    "type": "text",
                    "x": 460,
                    "y": 230,
                    "width": 80,
                    "height": 25,
                    "angle": 0,
                    "strokeColor": "#388e3c",
                    "backgroundColor": "transparent",
                    "fillStyle": "solid",
                    "strokeWidth": 2,
                    "strokeStyle": "solid",
                    "roughness": 1,
                    "opacity": 100,
                    "text": "🎉 Meetup\nSchema",
                    "fontSize": 16,
                    "fontFamily": 1,
                    "textAlign": "center",
                    "verticalAlign": "middle"
                },
                
                // Location Tracking Schema - Bottom Left
                {
                    "id": "location-schema",
                    "type": "rectangle",
                    "x": 150,
                    "y": 350,
                    "width": 200,
                    "height": 80,
                    "angle": 0,
                    "strokeColor": "#f57c00",
                    "backgroundColor": "#fff3e0",
                    "fillStyle": "solid",
                    "strokeWidth": 2,
                    "strokeStyle": "solid",
                    "roughness": 1,
                    "opacity": 100
                },
                {
                    "id": "location-text",
                    "type": "text",
                    "x": 210,
                    "y": 380,
                    "width": 80,
                    "height": 25,
                    "angle": 0,
                    "strokeColor": "#f57c00",
                    "backgroundColor": "transparent",
                    "fillStyle": "solid",
                    "strokeWidth": 2,
                    "strokeStyle": "solid",
                    "roughness": 1,
                    "opacity": 100,
                    "text": "📍 Location\nTracking",
                    "fontSize": 16,
                    "fontFamily": 1,
                    "textAlign": "center",
                    "verticalAlign": "middle"
                },
                
                // Message Schema - Bottom Center
                {
                    "id": "message-schema",
                    "type": "rectangle",
                    "x": 400,
                    "y": 350,
                    "width": 200,
                    "height": 80,
                    "angle": 0,
                    "strokeColor": "#c2185b",
                    "backgroundColor": "#fce4ec",
                    "fillStyle": "solid",
                    "strokeWidth": 2,
                    "strokeStyle": "solid",
                    "roughness": 1,
                    "opacity": 100
                },
                {
                    "id": "message-text",
                    "type": "text",
                    "x": 460,
                    "y": 380,
                    "width": 80,
                    "height": 25,
                    "angle": 0,
                    "strokeColor": "#c2185b",
                    "backgroundColor": "transparent",
                    "fillStyle": "solid",
                    "strokeWidth": 2,
                    "strokeStyle": "solid",
                    "roughness": 1,
                    "opacity": 100,
                    "text": "💬 Message\nSchema",
                    "fontSize": 16,
                    "fontFamily": 1,
                    "textAlign": "center",
                    "verticalAlign": "middle"
                },
                
                // Notification Schema - Right side
                {
                    "id": "notification-schema",
                    "type": "rectangle",
                    "x": 700,
                    "y": 200,
                    "width": 200,
                    "height": 80,
                    "angle": 0,
                    "strokeColor": "#689f38",
                    "backgroundColor": "#f1f8e9",
                    "fillStyle": "solid",
                    "strokeWidth": 2,
                    "strokeStyle": "solid",
                    "roughness": 1,
                    "opacity": 100
                },
                {
                    "id": "notification-text",
                    "type": "text",
                    "x": 760,
                    "y": 230,
                    "width": 80,
                    "height": 25,
                    "angle": 0,
                    "strokeColor": "#689f38",
                    "backgroundColor": "transparent",
                    "fillStyle": "solid",
                    "strokeWidth": 2,
                    "strokeStyle": "solid",
                    "roughness": 1,
                    "opacity": 100,
                    "text": "🔔 Notification\nSchema",
                    "fontSize": 16,
                    "fontFamily": 1,
                    "textAlign": "center",
                    "verticalAlign": "middle"
                },
                
                // Arrows - User to Group
                {
                    "id": "arrow-user-group",
                    "type": "arrow",
                    "x": 400,
                    "y": 120,
                    "width": 120,
                    "height": 100,
                    "angle": 0,
                    "strokeColor": "#666",
                    "backgroundColor": "transparent",
                    "fillStyle": "solid",
                    "strokeWidth": 2,
                    "strokeStyle": "solid",
                    "roughness": 1,
                    "opacity": 100,
                    "points": [[0, 0], [-120, 100]],
                    "lastCommittedPoint": [-120, 100],
                    "startArrowhead": null,
                    "endArrowhead": "arrow"
                },
                
                // Arrow - User to Meetup
                {
                    "id": "arrow-user-meetup",
                    "type": "arrow",
                    "x": 500,
                    "y": 130,
                    "width": 0,
                    "height": 70,
                    "angle": 0,
                    "strokeColor": "#666",
                    "backgroundColor": "transparent",
                    "fillStyle": "solid",
                    "strokeWidth": 2,
                    "strokeStyle": "solid",
                    "roughness": 1,
                    "opacity": 100,
                    "points": [[0, 0], [0, 70]],
                    "lastCommittedPoint": [0, 70],
                    "startArrowhead": null,
                    "endArrowhead": "arrow"
                },
                
                // Arrow - Meetup to Location
                {
                    "id": "arrow-meetup-location",
                    "type": "arrow",
                    "x": 450,
                    "y": 280,
                    "width": 100,
                    "height": 70,
                    "angle": 0,
                    "strokeColor": "#666",
                    "backgroundColor": "transparent",
                    "fillStyle": "solid",
                    "strokeWidth": 2,
                    "strokeStyle": "solid",
                    "roughness": 1,
                    "opacity": 100,
                    "points": [[0, 0], [-100, 70]],
                    "lastCommittedPoint": [-100, 70],
                    "startArrowhead": null,
                    "endArrowhead": "arrow"
                },
                
                // Arrow - Meetup to Message
                {
                    "id": "arrow-meetup-message",
                    "type": "arrow",
                    "x": 500,
                    "y": 280,
                    "width": 0,
                    "height": 70,
                    "angle": 0,
                    "strokeColor": "#666",
                    "backgroundColor": "transparent",
                    "fillStyle": "solid",
                    "strokeWidth": 2,
                    "strokeStyle": "solid",
                    "roughness": 1,
                    "opacity": 100,
                    "points": [[0, 0], [0, 70]],
                    "lastCommittedPoint": [0, 70],
                    "startArrowhead": null,
                    "endArrowhead": "arrow"
                },
                
                // Arrow - Meetup to Notification
                {
                    "id": "arrow-meetup-notification",
                    "type": "arrow",
                    "x": 600,
                    "y": 240,
                    "width": 100,
                    "height": 0,
                    "angle": 0,
                    "strokeColor": "#666",
                    "backgroundColor": "transparent",
                    "fillStyle": "solid",
                    "strokeWidth": 2,
                    "strokeStyle": "solid",
                    "roughness": 1,
                    "opacity": 100,
                    "points": [[0, 0], [100, 0]],
                    "lastCommittedPoint": [100, 0],
                    "startArrowhead": null,
                    "endArrowhead": "arrow"
                },
                
                // Key Features boxes
                {
                    "id": "features-box",
                    "type": "rectangle",
                    "x": 50,
                    "y": 500,
                    "width": 850,
                    "height": 120,
                    "angle": 0,
                    "strokeColor": "#1976d2",
                    "backgroundColor": "#f8f9ff",
                    "fillStyle": "solid",
                    "strokeWidth": 2,
                    "strokeStyle": "dashed",
                    "roughness": 1,
                    "opacity": 100
                },
                {
                    "id": "features-text",
                    "type": "text",
                    "x": 70,
                    "y": 520,
                    "width": 810,
                    "height": 80,
                    "angle": 0,
                    "strokeColor": "#1976d2",
                    "backgroundColor": "transparent",
                    "fillStyle": "solid",
                    "strokeWidth": 2,
                    "strokeStyle": "solid",
                    "roughness": 1,
                    "opacity": 100,
                    "text": "🔗 KEY RELATIONSHIPS & DATA FLOW:\n• Users create Groups & Meetups • Groups have Members with Roles\n• Meetups trigger LocationTracking • Real-time location updates\n• Messages enable chat within meetups • All events generate Notifications\n• RSVP system tracks participant responses • Geofencing for arrival detection",
                    "fontSize": 14,
                    "fontFamily": 1,
                    "textAlign": "left",
                    "verticalAlign": "top"
                }
            ],
            "appState": {
                "gridSize": null,
                "viewBackgroundColor": "#ffffff"
            },
            "files": {}
        };

        // Initialize Excalidraw
        const excalidrawContainer = document.getElementById('excalidraw-container');
        
        ReactDOM.render(
            React.createElement(ExcalidrawLib.Excalidraw, {
                initialData: initialData,
                onChange: (elements, appState) => {
                    // Auto-save functionality could be added here
                    console.log('Diagram updated');
                },
                UIOptions: {
                    canvasActions: {
                        loadScene: false,
                        export: true,
                        saveToActiveFile: false,
                        toggleTheme: true
                    }
                }
            }),
            excalidrawContainer
        );
    </script>
</body>
</html>