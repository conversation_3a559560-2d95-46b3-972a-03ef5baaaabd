const Joi = require('joi');
const { objectId } = require('./custom.validation');

const createGroup = {
  body: Joi.object().keys({
    name: Joi.string().required().trim(),
    description: Joi.string().trim(),
    avatar: Joi.string().uri(),
    settings: Joi.object().keys({
      isPrivate: Joi.boolean(),
      allowMemberInvites: Joi.boolean(),
      requireApproval: Joi.boolean(),
    }),
  }),
};

const getGroups = {
  query: Joi.object().keys({
    name: Joi.string(),
    createdBy: Joi.string().custom(objectId),
    isPrivate: Joi.boolean(),
    sortBy: Joi.string(),
    limit: Joi.number().integer(),
    page: Joi.number().integer(),
  }),
};

const getGroup = {
  params: Joi.object().keys({
    groupId: Joi.string().custom(objectId).allow(''),
  }),
};

const updateGroup = {
  params: Joi.object().keys({
    groupId: Joi.required().custom(objectId),
  }),
  body: Joi.object()
    .keys({
      name: Joi.string().trim(),
      description: Joi.string().trim(),
      avatar: Joi.string().uri(),
      settings: Joi.object().keys({
        isPrivate: Joi.boolean(),
        allowMemberInvites: Joi.boolean(),
        requireApproval: Joi.boolean(),
      }),
    })
    .min(1),
};

const deleteGroup = {
  params: Joi.object().keys({
    groupId: Joi.string().custom(objectId),
  }),
};

const addMember = {
  params: Joi.object().keys({
    groupId: Joi.string().custom(objectId),
  }),
  body: Joi.object().keys({
    userId: Joi.string().required().custom(objectId),
    role: Joi.string().valid('Admin', 'Moderator', 'Member').default('Member'),
  }),
};

const removeMember = {
  params: Joi.object().keys({
    groupId: Joi.string().custom(objectId),
    userId: Joi.string().custom(objectId),
  }),
};

module.exports = {
  createGroup,
  getGroups,
  getGroup,
  updateGroup,
  deleteGroup,
  addMember,
  removeMember,
};
