const httpStatus = require('http-status');
const tokenService = require('./token.service');
const userService = require('./user.service');
const Token = require('../models/token.model');
const ApiError = require('../utils/ApiError');
const { tokenTypes } = require('../config/tokens');
const dayjs = require('../config/dayjs'); // Use configured dayjs

/**
 * Login with username and password
 * @param {string} email
 * @param {string} password
 * @returns {Promise<User>}
 */
const loginUserWithEmailAndPassword = async (email, password) => {
  const user = await userService.getUserByEmail(email);
  if (!user || !(await user.isPasswordMatch(password))) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'Incorrect email or password');
  }
  return user;
};

/**
 * Logout
 * @param {string} refreshToken
 * @returns {Promise}
 */
const logout = async (refreshToken) => {
  const refreshTokenDoc = await Token.findOne({ token: refreshToken, type: tokenTypes.REFRESH, blacklisted: false });
  if (!refreshTokenDoc) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Not found');
  }
  await refreshTokenDoc.remove();
};

/**
 * Refresh auth tokens
 * @param {string} refreshToken
 * @returns {Promise<Object>}
 */
const refreshAuth = async (refreshToken) => {
  try {
    const refreshTokenDoc = await tokenService.verifyToken(refreshToken, tokenTypes.REFRESH);
    const user = await userService.getUserById(refreshTokenDoc.user);
    if (!user) {
      throw new Error();
    }
    await refreshTokenDoc.remove();
    return tokenService.generateAuthTokens(user);
  } catch (error) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'Please authenticate');
  }
};

/**
 * Reset password
 * @param {string} resetPasswordToken
 * @param {string} newPassword
 * @returns {Promise}
 */
const resetPassword = async (resetPasswordToken, newPassword) => {
  try {
    const resetPasswordTokenDoc = await tokenService.verifyToken(resetPasswordToken, tokenTypes.RESET_PASSWORD);
    const user = await userService.getUserById(resetPasswordTokenDoc.user);
    if (!user) {
      throw new Error();
    }
    await userService.updateUserById(user.id, { password: newPassword });
    await Token.deleteMany({ user: user.id, type: tokenTypes.RESET_PASSWORD });
  } catch (error) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'Password reset failed');
  }
};

/**
 * Verify email
 * @param {string} verifyEmailToken
 * @returns {Promise}
 */
const verifyEmail = async (verifyEmailToken) => {
  try {
    const verifyEmailTokenDoc = await tokenService.verifyToken(verifyEmailToken, tokenTypes.VERIFY_EMAIL);
    const user = await userService.getUserById(verifyEmailTokenDoc.user);
    if (!user) {
      throw new Error();
    }
    await Token.deleteMany({ user: user.id, type: tokenTypes.VERIFY_EMAIL });
    await userService.updateUserById(user.id, { isEmailVerified: true });
  } catch (error) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'Email verification failed');
  }
};

/**
 * Send OTP to phone number
 * @param {string} phoneNumber
 * @returns {Promise<Object>}
 */
const sendOTP = async (phoneNumber) => {
  try {
    // Validate phone number format
    if (!phoneNumber || phoneNumber.length < 10) {
      throw new Error('Invalid phone number format');
    }

    // Generate 6-digit OTP
    const otp = '000000'; // Math.floor(100000 + Math.random() * 900000).toString();

    // Store OTP in database with expiration (5 minutes)
    const expires = dayjs().add(5, 'minutes');
    // eslint-disable-next-line no-console
    console.log('Saving OTP:', { otp, phoneNumber, expires: expires.toDate() });

    await tokenService.saveToken(otp, phoneNumber, expires, tokenTypes.OTP);

    return {
      message: 'OTP sent successfully',
      otp: process.env.NODE_ENV === 'development' ? otp : undefined,
    };
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('SendOTP Error:', error);
    throw new ApiError(httpStatus.BAD_REQUEST, `Failed to send OTP: ${error.message}`);
  }
};

/**
 * Verify OTP and login/register user
 * @param {string} phoneNumber
 * @param {string} otp
 * @returns {Promise<User>}
 */
const verifyOTP = async (phoneNumber, otp) => {
  try {
    // Verify OTP token
    const otpTokenDoc = await tokenService.verifyToken(otp, tokenTypes.OTP);

    // For OTP tokens, user field contains the phone number
    if (otpTokenDoc.user !== phoneNumber) {
      throw new Error('Invalid OTP for this phone number');
    }

    // Delete used OTP
    await Token.deleteMany({ user: phoneNumber, type: tokenTypes.OTP });

    // Check if user exists, if not create new user
    let user = await userService.getUserByPhone(phoneNumber);

    if (!user) {
      // Create new user with phone number
      user = await userService.createUser({
        phoneNumber,
        name: `User_${phoneNumber.slice(-4)}`,
        isPhoneVerified: true,
      });
    } else {
      // Update phone verification status
      await userService.updateUserById(user.id, { isPhoneVerified: true });
      user = await userService.getUserById(user.id);
    }

    return user;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);
    throw new ApiError(httpStatus.UNAUTHORIZED, 'OTP verification failed');
  }
};

module.exports = {
  loginUserWithEmailAndPassword,
  logout,
  refreshAuth,
  resetPassword,
  verifyEmail,
  sendOTP,
  verifyOTP,
};
