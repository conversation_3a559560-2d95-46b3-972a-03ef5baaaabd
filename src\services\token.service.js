const jwt = require('jsonwebtoken');
const httpStatus = require('http-status');
const dayjs = require('../config/dayjs'); // Use configured dayjs
const config = require('../config/config');
const userService = require('./user.service');
const { Token } = require('../models');
const ApiError = require('../utils/ApiError');
const { tokenTypes } = require('../config/tokens');

/**
 * Generate token
 * @param {ObjectId} userId
 * @param {dayjs.Dayjs} expires
 * @param {string} type
 * @param {string} [secret]
 * @returns {string}
 */
const generateToken = (userId, expires, type, secret = config.jwt.secret) => {
  const payload = {
    sub: userId,
    iat: dayjs().unix(),
    exp: expires.unix(),
    type,
  };
  return jwt.sign(payload, secret);
};

/**
 * Save a token
 * @param {string} token
 * @param {ObjectId} userId
 * @param {dayjs.Dayjs} expires
 * @param {string} type
 * @param {boolean} [blacklisted]
 * @returns {Promise<Token>}
 */
const saveToken = async (token, userId, expires, type, blacklisted = false) => {
  // eslint-disable-next-line no-console
  console.log('saveToken: ', token);
  const tokenDoc = await Token.create({
    token,
    user: userId,
    expires: expires.toDate(),
    type,
    blacklisted,
  });
  // eslint-disable-next-line no-console
  console.log('tokenDoc: ', tokenDoc);
  return tokenDoc;
};

/**
 * Verify token and return token doc (or throw an error if it is not valid)
 * @param {string} token
 * @param {string} type
 * @returns {Promise<Token>}
 */
const verifyToken = async (token, type) => {
  // eslint-disable-next-line no-console
  console.log(`Verify Token - Type: ${type}, Token: ${token.substring(0, 20)}...`);

  // For OTP tokens, we don't use JWT verification
  if (type === tokenTypes.OTP) {
    const tokenDoc = await Token.findOne({
      token,
      type,
      blacklisted: false,
      expires: { $gt: new Date() }, // Check if token hasn't expired
    });
    // eslint-disable-next-line no-console
    console.log('OTP Token Doc:', tokenDoc);
    if (!tokenDoc) {
      throw new Error('Token not found or expired');
    }
    return tokenDoc;
  }

  // For ACCESS tokens, only verify JWT signature and expiration (stateless)
  if (type === tokenTypes.ACCESS) {
    try {
      const payload = jwt.verify(token, config.jwt.secret);
      // eslint-disable-next-line no-console
      console.log('Access Token Payload:', payload);

      // For access tokens, we don't check the database - they are stateless
      // Just return a mock token doc with the payload info
      return {
        token,
        user: payload.sub,
        type: payload.type,
        expires: new Date(payload.exp * 1000),
        blacklisted: false,
      };
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('Access Token Verification Error:', error.message);
      throw new Error('Invalid or expired token');
    }
  }

  // For other JWT tokens (REFRESH, RESET_PASSWORD, VERIFY_EMAIL), check database
  const payload = jwt.verify(token, config.jwt.secret);
  const tokenDoc = await Token.findOne({ token, type, user: payload.sub, blacklisted: false });
  if (!tokenDoc) {
    throw new Error('Token not found');
  }
  return tokenDoc;
};

/**
 * Generate auth tokens
 * @param {User} user
 * @returns {Promise<Object>}
 */
const generateAuthTokens = async (user) => {
  const accessTokenExpires = dayjs().add(config.jwt.accessExpirationMinutes, 'minutes');
  const accessToken = generateToken(user.id, accessTokenExpires, tokenTypes.ACCESS);

  const refreshTokenExpires = dayjs().add(config.jwt.refreshExpirationDays, 'days');
  const refreshToken = generateToken(user.id, refreshTokenExpires, tokenTypes.REFRESH);
  await saveToken(refreshToken, user.id, refreshTokenExpires, tokenTypes.REFRESH);

  return {
    access: {
      token: accessToken,
      expires: accessTokenExpires.toDate(),
    },
    refresh: {
      token: refreshToken,
      expires: refreshTokenExpires.toDate(),
    },
  };
};

/**
 * Generate reset password token
 * @param {string} email
 * @returns {Promise<string>}
 */
const generateResetPasswordToken = async (email) => {
  const user = await userService.getUserByEmail(email);
  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, 'No users found with this email');
  }
  const expires = dayjs().add(config.jwt.resetPasswordExpirationMinutes, 'minutes');
  const resetPasswordToken = generateToken(user.id, expires, tokenTypes.RESET_PASSWORD);
  await saveToken(resetPasswordToken, user.id, expires, tokenTypes.RESET_PASSWORD);
  return resetPasswordToken;
};

module.exports = {
  generateToken,
  saveToken,
  verifyToken,
  generateAuthTokens,
  generateResetPasswordToken,
};
